/* Estilos generales */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f0f2f5;
    color: #333;
}

.container {
    display: flex;
    min-height: 100vh;
    max-width: 100%; /* Utiliza todo el ancho disponible */
    margin: 0;
    background-color: #f0f2f5;
    justify-content: flex-start; /* Alinea los elementos al inicio */
    gap: 0; /* Elimina el espacio entre elementos */
    overflow-x: hidden; /* Evita el desplazamiento horizontal */
    position: relative; /* Necesario para el posicionamiento correcto de los elementos hijos */
}

/* Variables de color */
:root {
    --primary-color: #6a0dad;
    --primary-light: #8e44ad;
    --primary-dark: #4a0080;
    --secondary-color: #2ecc71;
    --accent-color: #ff3366;
    --background-color: #f0f2f5;
    --card-color: #ffffff;
    --text-color: #333333;
    --text-light: #666666;
    --border-color: #e0e0e0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --primary-shadow: rgba(106, 13, 173, 0.2);
}

/* Barra lateral izquierda */
.sidebar-left {
    background-color: var(--background-color);
    padding: 15px 0;
    position: fixed; /* Permanece fijo */
    top: 0;
    left: 0; /* Alineado a la izquierda */
    height: 100vh;
    width: 260px; /* Reducido para dar más espacio al contenido principal */
    overflow-y: auto; /* Tiene su propio scroll */
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    z-index: 100; /* Asegura que esté por encima de otros elementos */
    scrollbar-width: thin; /* Para Firefox */
    scrollbar-color: rgba(106, 13, 173, 0.3) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

/* Estilo para la barra de desplazamiento de la barra lateral izquierda (WebKit) */
.sidebar-left::-webkit-scrollbar {
    width: 6px;
}

.sidebar-left::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
}

.sidebar-left::-webkit-scrollbar-thumb {
    background-color: rgba(106, 13, 173, 0.3);
    border-radius: 6px;
}

.sidebar-left::-webkit-scrollbar-thumb:hover {
    background-color: rgba(106, 13, 173, 0.5);
}

.logo {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo-link {
    text-decoration: none;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
}

.logo-link:hover {
    transform: scale(1.05);
}

.logo-container {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    border-radius: 5px;
    box-shadow: 0 2px 5px var(--primary-shadow);
}

.sidebar-menu {
    padding: 0 20px; /* Aumentado aún más el padding horizontal para aprovechar el espacio adicional */
    width: 100%;
}

.menu-title {
    font-size: 12px;
    color: var(--text-light);
    margin: 20px 0 8px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu > ul > li {
    margin: 5px 0;
    padding: 6px 5px; /* Ajustado para coincidir con la imagen */
    border-radius: 10px;
    transition: all 0.3s;
}

.sidebar-menu li:hover {
    background-color: rgba(106, 13, 173, 0.15); /* Fondo con el mismo color que el logo */
    transform: translateX(2px); /* Pequeño efecto de movimiento al pasar el ratón */
}

.sidebar-menu a {
    text-decoration: none;
    color: #6a0dad; /* Exactamente el mismo color que el logo */
    display: flex;
    align-items: center;
    padding: 8px 15px; /* Aumentado aún más el padding horizontal para aprovechar el espacio adicional */
    border-radius: 8px;
    transition: all 0.3s;
    font-weight: 500;
    font-size: 17px; /* Aumentado un poco más */
}

.sidebar-menu a:hover {
    color: #6a0dad; /* Exactamente el mismo color que el logo */
    opacity: 0.9; /* Ligero cambio de opacidad al pasar el ratón */
}

.sidebar-menu a i {
    margin-right: 10px;
    font-size: 20px; /* Aumentado aún más para mayor visibilidad */
    width: 24px; /* Aumentado para dar más espacio al icono */
    text-align: center;
    color: #6a0dad; /* Exactamente el mismo color que el logo */
}

/* Estilos para el perfil en la barra lateral */
.profile-sidebar {
    display: flex;
    align-items: center;
    padding: 10px;
    margin-top: 20px;
    cursor: pointer;
    border-radius: 10px;
    transition: background-color 0.3s;
}

.profile-sidebar:hover {
    background-color: rgba(106, 13, 173, 0.05);
}

.profile-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e0e0e0;
    overflow: hidden;
    margin-right: 10px;
    border: 2px solid var(--primary-color);
}

.profile-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.profile-info span:first-child {
    font-weight: 500;
    font-size: 14px;
}

.teams-clubs {
    font-size: 12px;
    color: var(--text-light);
}

.profile-menu-toggle {
    color: var(--text-light);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.profile-menu-toggle:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--text-color);
}

/* Submenu de deportes */
.has-submenu {
    position: relative;
}

.submenu {
    display: none;
    list-style: none;
    padding-left: 0; /* Eliminado el padding izquierdo */
    margin-top: 5px;
    margin-bottom: 5px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    overflow: visible; /* Cambiado a visible para evitar recortes */
    max-height: 0;
    transition: max-height 0.3s ease-in-out;
    width: 100%; /* Ancho completo */
    min-width: 310px; /* Ancho mínimo aumentado otro 10% para coincidir con el nuevo ancho de la barra lateral */
}

.submenu li {
    padding: 2px 0;
}

.submenu a {
    padding-left: 45px; /* Aumentado aún más el padding izquierdo para aprovechar el espacio adicional */
    font-size: 16px; /* Aumentado un poco más */
    color: #6a0dad; /* Exactamente el mismo color que el logo */
}

.submenu a i {
    font-size: 18px; /* Aumentado para mayor visibilidad */
    color: #6a0dad; /* Exactamente el mismo color que el logo */
}

.has-submenu:hover .submenu {
    display: block;
    max-height: 400px; /* Aumentado para mostrar más contenido */
}

/* Estilos para los tooltips en el menú AI Analysis */
.js-tooltip {
    position: fixed;
    width: 220px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 6px;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1001;
    line-height: 1.4;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    text-align: left;
}

.js-tooltip::before {
    content: "";
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent var(--tooltip-color, rgba(0, 0, 0, 0.8)) transparent transparent;
}

.js-tooltip.visible {
    opacity: 1;
    visibility: visible;
}

/* Contenido principal y barra lateral derecha - contenedor */
.main-content-and-sidebar {
    display: flex;
    margin-left: 260px; /* Espacio para la barra lateral izquierda */
    width: calc(100% - 260px); /* Ancho restante */
    min-height: 100vh;
}

/* Contenido principal */
.main-content {
    padding: 0;
    background-color: var(--background-color);
    width: 100%; /* Utiliza todo el ancho disponible */
    margin: 0;
    flex: 1; /* Ocupa todo el espacio disponible */
}

/* Barra lateral derecha */
.sidebar-right {
    padding: 20px 15px;
    background-color: var(--background-color);
    width: 280px; /* Ajustado para mantener el tamaño pero acercarlo a la zona central */
    margin-left: 0; /* Cambiado para acercar a la zona central */
    position: relative; /* Cambiado de sticky a relative para que se desplace con la página */
    overflow-y: visible; /* Cambiado de auto a visible para que no tenga su propio scroll */
}

/* Botones flotantes */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.chat-button, .top-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    margin-top: 10px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chat-button:hover, .top-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.chat-button {
    background-color: var(--primary-color);
    color: white;
}

.top-button {
    background-color: white;
    color: #333;
    border: 1px solid var(--border-color);
}

/* Responsive */
@media (max-width: 1024px) {
    .container {
        grid-template-columns: 220px 1fr;
    }
    .sidebar-right {
        display: none;
    }
}

@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
    }
    .sidebar-left {
        display: none;
    }
}

/* Badge para elementos nuevos */
.badge {
    display: inline-block;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    border-radius: 10px;
    margin-left: 5px;
}

.new-badge {
    background-color: #ff3366; /* Restaurado al color rosado original */
    color: white;
    box-shadow: 0 2px 4px rgba(255, 51, 102, 0.3);
    animation: pulse 2s infinite;
    transition: opacity 0.5s ease;
    font-size: 11px; /* Ajustado para mejor legibilidad */
    padding: 2px 7px; /* Un poco más de padding horizontal */
    border-radius: 12px; /* Bordes más redondeados */
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
