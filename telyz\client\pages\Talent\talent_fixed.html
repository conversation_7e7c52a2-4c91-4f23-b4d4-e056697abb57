<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Talents - Guardiola AI</title>
    <link rel="stylesheet" href="talent_fixed.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Sidebar Left -->
        <div class="sidebar-left">
            <!-- Logo -->
            <div class="logo">
                <a href="#" class="logo-link">
                    <div class="logo-container">T</div>
                </a>
            </div>
            
            <!-- Enhanced User Profile -->
            <div class="user-profile-simple">
                <div class="profile-image-container">
                    <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=150&h=150&fit=crop" alt="Profile Picture" class="profile-img">
                </div>
                
                <div class="profile-info">
                    <h3 class="user-name">Ahmed Hassan</h3>
                    <p class="user-club"><i class="fas fa-shield-alt"></i> Al Ahly SC</p>
                    <p class="user-category"><i class="fas fa-futbol"></i> Football - Forward</p>
                    
                    <!-- Additional Info -->
                    <div class="additional-info">
                        <div class="info-item">
                            <span class="info-label">Position:</span>
                            <span class="info-value">Striker</span>
                        </div>
                        <div class="club-logo-section">
                            <span class="info-label">Current Club:</span>
                            <div class="club-logo-container">
                                <img src="https://logos-world.net/wp-content/uploads/2020/06/Al-Ahly-Logo.png" alt="Al Ahly Logo" class="club-logo">
                                <span class="club-name">Al Ahly SC</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Info -->
                    <div class="additional-info">
                        <div class="info-item">
                            <span class="info-label">Position:</span>
                            <span class="info-value">Striker</span>
                        </div>
                        <div class="club-logo-section">
                            <span class="info-label">Current Club:</span>
                            <div class="club-logo-container">
                                <img src="https://logos-world.net/wp-content/uploads/2020/06/Al-Ahly-Logo.png" alt="Al Ahly Logo" class="club-logo">
                                <span class="club-name">Al Ahly SC</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Talents Header -->
            <div class="talents-header">
                <h1><i class="fas fa-star"></i> Discover Talents</h1>
                <p class="subtitle">Find and connect with the best athletes in your sport</p>
            </div>

            <!-- Talent Categories Tabs -->
            <div class="talent-categories">
                <div class="tab-buttons">
                    <div class="tab-btn active" data-tab="community">
                        <i class="fas fa-users"></i>
                        <span>Community Leaders</span>
                        <small>Most Active</small>
                    </div>
                    <div class="tab-btn" data-tab="ai-scouted">
                        <i class="fas fa-robot"></i>
                        <span>AI Scouted</span>
                        <small>AI Selected</small>
                    </div>
                    <div class="tab-btn" data-tab="expert-curated">
                        <i class="fas fa-medal"></i>
                        <span>Expert Curated</span>
                        <small>Pro Recommended</small>
                    </div>
                </div>

                <!-- Search and Filters -->
                <div class="search-filters-section">
                    <div class="search-bar">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search talents by name, sport, or location...">
                    </div>
                    
                    <div class="filter-controls">
                        <select class="filter-select">
                            <option value="">All Sports</option>
                            <option value="football">Football</option>
                            <option value="basketball">Basketball</option>
                            <option value="handball">Handball</option>
                            <option value="volleyball">Volleyball</option>
                            <option value="baseball">Baseball</option>
                            <option value="field-hockey">Field Hockey</option>
                            <option value="cricket">Cricket</option>
                        </select>
                        <select class="filter-select">
                            <option value="">All Countries</option>
                            <option value="afghanistan">Afghanistan</option>
                            <option value="albania">Albania</option>
                            <option value="algeria">Algeria</option>
                            <option value="argentina">Argentina</option>
                            <option value="armenia">Armenia</option>
                            <option value="australia">Australia</option>
                            <option value="austria">Austria</option>
                            <option value="azerbaijan">Azerbaijan</option>
                            <option value="bahrain">Bahrain</option>
                            <option value="bangladesh">Bangladesh</option>
                            <option value="belarus">Belarus</option>
                            <option value="belgium">Belgium</option>
                            <option value="bolivia">Bolivia</option>
                            <option value="bosnia">Bosnia and Herzegovina</option>
                            <option value="brazil">Brazil</option>
                            <option value="bulgaria">Bulgaria</option>
                            <option value="cambodia">Cambodia</option>
                            <option value="cameroon">Cameroon</option>
                            <option value="canada">Canada</option>
                            <option value="chile">Chile</option>
                            <option value="china">China</option>
                            <option value="colombia">Colombia</option>
                            <option value="croatia">Croatia</option>
                            <option value="czech">Czech Republic</option>
                            <option value="denmark">Denmark</option>
                            <option value="ecuador">Ecuador</option>
                            <option value="egypt">Egypt</option>
                            <option value="england">England</option>
                            <option value="estonia">Estonia</option>
                            <option value="ethiopia">Ethiopia</option>
                            <option value="finland">Finland</option>
                            <option value="france">France</option>
                            <option value="georgia">Georgia</option>
                            <option value="germany">Germany</option>
                            <option value="ghana">Ghana</option>
                            <option value="greece">Greece</option>
                            <option value="hungary">Hungary</option>
                            <option value="iceland">Iceland</option>
                            <option value="india">India</option>
                            <option value="indonesia">Indonesia</option>
                            <option value="iran">Iran</option>
                            <option value="iraq">Iraq</option>
                            <option value="ireland">Ireland</option>
                            <option value="israel">Israel</option>
                            <option value="italy">Italy</option>
                            <option value="japan">Japan</option>
                            <option value="jordan">Jordan</option>
                            <option value="kazakhstan">Kazakhstan</option>
                            <option value="kenya">Kenya</option>
                            <option value="kuwait">Kuwait</option>
                            <option value="lebanon">Lebanon</option>
                            <option value="libya">Libya</option>
                            <option value="lithuania">Lithuania</option>
                            <option value="malaysia">Malaysia</option>
                            <option value="mexico">Mexico</option>
                            <option value="morocco">Morocco</option>
                            <option value="netherlands">Netherlands</option>
                            <option value="new-zealand">New Zealand</option>
                            <option value="nigeria">Nigeria</option>
                            <option value="norway">Norway</option>
                            <option value="oman">Oman</option>
                            <option value="pakistan">Pakistan</option>
                            <option value="palestine">Palestine</option>
                            <option value="peru">Peru</option>
                            <option value="philippines">Philippines</option>
                            <option value="poland">Poland</option>
                            <option value="portugal">Portugal</option>
                            <option value="qatar">Qatar</option>
                            <option value="romania">Romania</option>
                            <option value="russia">Russia</option>
                            <option value="saudi-arabia">Saudi Arabia</option>
                            <option value="scotland">Scotland</option>
                            <option value="senegal">Senegal</option>
                            <option value="serbia">Serbia</option>
                            <option value="singapore">Singapore</option>
                            <option value="slovakia">Slovakia</option>
                            <option value="slovenia">Slovenia</option>
                            <option value="south-africa">South Africa</option>
                            <option value="south-korea">South Korea</option>
                            <option value="spain">Spain</option>
                            <option value="sri-lanka">Sri Lanka</option>
                            <option value="sudan">Sudan</option>
                            <option value="sweden">Sweden</option>
                            <option value="switzerland">Switzerland</option>
                            <option value="syria">Syria</option>
                            <option value="thailand">Thailand</option>
                            <option value="tunisia">Tunisia</option>
                            <option value="turkey">Turkey</option>
                            <option value="uae">United Arab Emirates</option>
                            <option value="ukraine">Ukraine</option>
                            <option value="usa">United States</option>
                            <option value="uruguay">Uruguay</option>
                            <option value="uzbekistan">Uzbekistan</option>
                            <option value="venezuela">Venezuela</option>
                            <option value="vietnam">Vietnam</option>
                            <option value="wales">Wales</option>
                            <option value="yemen">Yemen</option>
                        </select>
                        <select class="filter-select">
                            <option value="">All Positions</option>
                            <option value="goalkeeper">Goalkeeper</option>
                            <option value="center-back">Center Back</option>
                            <option value="central-defender">Central Defender</option>
                            <option value="right-back">Right Back</option>
                            <option value="left-back">Left Back</option>
                            <option value="midfielder">Midfielder</option>
                            <option value="playmaker">Playmaker</option>
                            <option value="striker">Striker</option>
                            <option value="right-winger">Right Winger</option>
                            <option value="left-winger">Left Winger</option>
                        </select>
                        <select class="filter-select">
                            <option value="">All Levels</option>
                            <option value="professional">Professional</option>
                            <option value="semi-pro">Semi-Professional</option>
                            <option value="amateur">Amateur</option>
                        </select>
                        <button class="apply-filters-btn" onclick="applyFilters()">
                            <i class="fas fa-check"></i>
                            Done
                        </button>
                    </div>
                </div>

                <!-- Tab Content -->
                <div class="talent-content-area">
                    <div class="tab-content active" id="community">
                        <div class="category-title">
                            <i class="fas fa-users"></i>
                            Community Leaders
                        </div>
                        <p class="category-description">The most active and engaged athletes in our community</p>
                        <div class="talents-grid" id="community-talents">
                            <div class="loading-placeholder">
                                <i class="fas fa-spinner fa-pulse"></i>
                                <p>Loading community leaders...</p>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="ai-scouted">
                        <div class="category-title">
                            <i class="fas fa-robot"></i>
                            AI Scouted Talents
                        </div>
                        <p class="category-description">Athletes selected by our AI based on performance data and potential</p>
                        <div class="talents-grid" id="ai-scouted-talents">
                            <div class="loading-placeholder">
                                <i class="fas fa-spinner fa-pulse"></i>
                                <p>Loading AI scouted talents...</p>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="expert-curated">
                        <div class="category-title">
                            <i class="fas fa-medal"></i>
                            Expert Curated
                        </div>
                        <p class="category-description">Hand-picked by professional scouts and coaches</p>
                        <div class="talents-grid" id="expert-curated-talents">
                            <div class="loading-placeholder">
                                <i class="fas fa-spinner fa-pulse"></i>
                                <p>Loading expert curated talents...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar Right -->
        <div class="sidebar-right">
            <!-- Top Performers Widget -->
            <div class="widget">
                <h3><i class="fas fa-trophy"></i> Top Performers</h3>
                <div class="top-performers-list">
                    <div class="performer-item">
                        <div class="performer-rank">1</div>
                        <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=50&h=50&fit=crop" alt="Player">
                        <div class="performer-info">
                            <strong>Ahmed Hassan</strong>
                            <span>Football Forward</span>
                        </div>
                        <div class="performer-score">9.2</div>
                    </div>
                    <div class="performer-item">
                        <div class="performer-rank">2</div>
                        <img src="https://images.unsplash.com/photo-1566753323558-f4e0952af115?w=50&h=50&fit=crop" alt="Player">
                        <div class="performer-info">
                            <strong>Carlos Rodriguez</strong>
                            <span>Basketball Guard</span>
                        </div>
                        <div class="performer-score">8.9</div>
                    </div>
                    <div class="performer-item">
                        <div class="performer-rank">3</div>
                        <img src="https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?w=50&h=50&fit=crop" alt="Player">
                        <div class="performer-info">
                            <strong>Marcus Johnson</strong>
                            <span>Baseball Pitcher</span>
                        </div>
                        <div class="performer-score">8.7</div>
                    </div>
                </div>
            </div>

            <!-- Statistics Widget -->
            <div class="widget">
                <h3><i class="fas fa-chart-bar"></i> Platform Stats</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">2,847</div>
                        <div class="stat-label">Total Athletes</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">15</div>
                        <div class="stat-label">Sports Categories</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">892</div>
                        <div class="stat-label">AI Analyses</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">156</div>
                        <div class="stat-label">Scout Reports</div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity Widget -->
            <div class="widget">
                <h3><i class="fas fa-clock"></i> Recent Activity</h3>
                <div class="activity-list">
                    <div class="activity-item">
                        <i class="fas fa-user-plus"></i>
                        <div class="activity-content">
                            <strong>Omar Al-Masri</strong> joined as a new talent
                            <span class="activity-time">2 hours ago</span>
                        </div>
                    </div>
                    <div class="activity-item">
                        <i class="fas fa-trophy"></i>
                        <div class="activity-content">
                            <strong>Hassan Mohamed</strong> achieved top rating
                            <span class="activity-time">4 hours ago</span>
                        </div>
                    </div>
                    <div class="activity-item">
                        <i class="fas fa-robot"></i>
                        <div class="activity-content">
                            AI analysis completed for <strong>3 new athletes</strong>
                            <span class="activity-time">6 hours ago</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Featured Sports Widget -->
            <div class="widget">
                <h3><i class="fas fa-fire"></i> Trending Sports</h3>
                <div class="trending-sports">
                    <div class="sport-item">
                        <i class="fas fa-futbol"></i>
                        <span>Football</span>
                        <div class="sport-trend">↗ +15%</div>
                    </div>
                    <div class="sport-item">
                        <i class="fas fa-basketball-ball"></i>
                        <span>Basketball</span>
                        <div class="sport-trend">↗ +12%</div>
                    </div>
                    <div class="sport-item">
                        <i class="fas fa-hand-paper"></i>
                        <span>Handball</span>
                        <div class="sport-trend">↗ +8%</div>
                    </div>
                    <div class="sport-item">
                        <i class="fas fa-volleyball-ball"></i>
                        <span>Volleyball</span>
                        <div class="sport-trend">↗ +6%</div>
                    </div>
                    <div class="sport-item">
                        <i class="fas fa-baseball-ball"></i>
                        <span>Baseball</span>
                        <div class="sport-trend">↗ +4%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab switching functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));
                    
                    // Add active class to clicked button and corresponding content
                    button.classList.add('active');
                    const tabId = button.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                    
                    // Load talents for the selected category
                    loadTalentsForCategory(tabId);
                });
            });
            
            // Load initial talents
            loadTalentsForCategory('community');
        });

        // Load talents for specific category
        function loadTalentsForCategory(category) {
            const container = document.getElementById(category + '-talents');
            
            // Show loading
            container.innerHTML = '<div class="loading-placeholder"><i class="fas fa-spinner fa-pulse"></i><p>Loading talents...</p></div>';
            
            // Simulate API call
            setTimeout(() => {
                const talents = getSampleTalentsForCategory(category);
                renderTalents(talents, container);
            }, 1000);
        }

        // Render talents
        function renderTalents(talents, container) {
            const talentsHTML = talents.map(talent => `
                <div class="talent-card">
                    <div class="talent-image">
                        <img src="${talent.image}" alt="${talent.name}">
                        <div class="talent-badge">${talent.badge}</div>
                    </div>
                    <div class="talent-info">
                        <h3>${talent.name}</h3>
                        <p><i class="fas fa-running"></i> ${talent.sport} - ${talent.position}</p>
                        <p><i class="fas fa-map-marker-alt"></i> ${talent.location}</p>
                        <div class="talent-rating">
                            <div class="rating-circle">${talent.rating}</div>
                            <div class="rating-label">Overall Rating</div>
                        </div>
                        <div class="talent-actions">
                            <button class="btn-primary">
                                <i class="fas fa-eye"></i>
                                View Profile
                            </button>
                            <button class="btn-secondary">
                                <i class="fas fa-envelope"></i>
                                Message
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = talentsHTML;
        }

        // Get sample talents based on category
        function getSampleTalentsForCategory(category) {
            const baseTalents = [
                {
                    name: "Ahmed Hassan",
                    sport: "Football",
                    position: "Forward",
                    location: "Cairo, Egypt",
                    image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=300&fit=crop",
                    rating: 9.2,
                    badge: category === 'community' ? 'Community Leader' : category === 'ai-scouted' ? 'AI Rated 9.2' : 'Expert Pick'
                },
                {
                    name: "Omar Al-Masri",
                    sport: "Handball",
                    position: "Left Wing",
                    location: "Amman, Jordan",
                    image: "https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?w=300&h=300&fit=crop",
                    rating: 8.9,
                    badge: category === 'community' ? 'Most Active' : category === 'ai-scouted' ? 'AI Rated 8.9' : 'Scout Recommended'
                },
                {
                    name: "Carlos Rodriguez",
                    sport: "Basketball",
                    position: "Point Guard",
                    location: "Madrid, Spain",
                    image: "https://images.unsplash.com/photo-1566753323558-f4e0952af115?w=300&h=300&fit=crop",
                    rating: 8.7,
                    badge: category === 'community' ? 'Rising Star' : category === 'ai-scouted' ? 'AI Rated 8.7' : 'Coach Choice'
                },
                {
                    name: "Hassan Mohamed",
                    sport: "Cricket",
                    position: "All-rounder",
                    location: "Tunis, Tunisia",
                    image: "https://images.unsplash.com/photo-1594736797933-d0ca71608884?w=300&h=300&fit=crop",
                    rating: 8.5,
                    badge: category === 'community' ? 'Mentor' : category === 'ai-scouted' ? 'AI Rated 8.5' : 'Pro Recommended'
                },
                {
                    name: "Marco Silva",
                    sport: "Volleyball",
                    position: "Spiker",
                    location: "Lisbon, Portugal",
                    image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop",
                    rating: 8.3,
                    badge: category === 'community' ? 'Team Player' : category === 'ai-scouted' ? 'AI Rated 8.3' : 'Coach Pick'
                },
                {
                    name: "Youssef Bennani",
                    sport: "Field Hockey",
                    position: "Forward",
                    location: "Casablanca, Morocco",
                    image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=300&fit=crop",
                    rating: 8.1,
                    badge: category === 'community' ? 'Fast Riser' : category === 'ai-scouted' ? 'AI Rated 8.1' : 'Scout Choice'
                }
            ];
            
            return baseTalents;
        }

        // Apply filters function
        function applyFilters() {
            const sportFilter = document.querySelector('.filter-controls select:nth-child(1)').value;
            const countryFilter = document.querySelector('.filter-controls select:nth-child(2)').value;
            const positionFilter = document.querySelector('.filter-controls select:nth-child(3)').value;
            const levelFilter = document.querySelector('.filter-controls select:nth-child(4)').value;
            const searchTerm = document.querySelector('.search-bar input').value;
            
            // Show loading while applying filters
            const activeTab = document.querySelector('.tab-content.active');
            const talentsGrid = activeTab.querySelector('.talents-grid');
            
            talentsGrid.innerHTML = '<div class="loading-placeholder"><i class="fas fa-spinner fa-pulse"></i><p>Applying filters...</p></div>';
            
            // Simulate filter application
            setTimeout(() => {
                // Get current active tab
                const activeTabId = document.querySelector('.tab-btn.active').getAttribute('data-tab');
                loadTalentsForCategory(activeTabId);
                
                // Show success message briefly
                const button = document.querySelector('.apply-filters-btn');
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check-circle"></i> Applied!';
                button.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.style.background = 'linear-gradient(135deg, #6a0dad, #8e44ad)';
                }, 1500);
                
            }, 800);
            
            console.log('Filters Applied:', {
                sport: sportFilter || 'All Sports',
                country: countryFilter || 'All Countries',
                position: positionFilter || 'All Positions',
                level: levelFilter || 'All Levels',
                search: searchTerm || 'No search term'
            });
        }
    </script>
</body>
</html>